buildscript {
    ext.kotlin_version = '1.6.21'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.2.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.3.15'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

// تم تعليق تكوين مسار البناء المخصص لحل المشكلة
// def newBuildDir = new File(rootProject.projectDir, "../build")
// rootProject.buildDir = newBuildDir
//
// subprojects {
//     project.buildDir = new File("${newBuildDir}", project.name)
// }

task clean(type: Delete) {
    delete rootProject.buildDir
}
