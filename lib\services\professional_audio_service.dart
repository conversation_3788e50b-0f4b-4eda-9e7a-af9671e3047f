import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:audioplayers/audioplayers.dart';
import 'audio_generator_service.dart';
import 'audio_file_manager.dart';

/// خدمة الصوت الاحترافية مثل Duolingo
class ProfessionalAudioService {
  static final ProfessionalAudioService _instance =
      ProfessionalAudioService._internal();
  factory ProfessionalAudioService() => _instance;
  ProfessionalAudioService._internal();

  final FlutterTts _tts = FlutterTts();
  final AudioPlayer _audioPlayer = AudioPlayer();
  final AudioPlayer _effectsPlayer = AudioPlayer();
  final AudioGeneratorService _audioGenerator = AudioGeneratorService();
  final LanguageAudioService _languageAudio = LanguageAudioService();
  final BackgroundMusicService _backgroundMusic = BackgroundMusicService();
  final AudioFileManager _fileManager = AudioFileManager();

  bool _isInitialized = false;
  String _currentLanguage = 'ar';
  double _speechRate = 0.5;
  double _pitch = 1.0;
  final double _volume = 1.0;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة مدير الملفات الصوتية
      await _fileManager.initialize();

      // تهيئة TTS
      await _tts.setLanguage(_currentLanguage);
      await _tts.setSpeechRate(_speechRate);
      await _tts.setPitch(_pitch);
      await _tts.setVolume(_volume);

      // إعداد المتحدثين الطبيعيين
      await _setupNativeVoices();

      _isInitialized = true;
      debugPrint('✅ Professional Audio Service initialized');

      // طباعة إحصائيات الملفات الصوتية
      final stats = await _fileManager.getAudioFilesStats();
      debugPrint('🎵 Audio Files: ${stats.toString()}');
    } catch (e) {
      debugPrint('❌ Error initializing audio service: $e');
    }
  }

  /// إعداد الأصوات الطبيعية
  Future<void> _setupNativeVoices() async {
    final voices = await _tts.getVoices;
    if (voices != null) {
      // البحث عن أفضل صوت لكل لغة
      final languageVoices = {
        'ar': _findBestVoice(voices, ['ar-SA', 'ar-EG', 'ar']),
        'en': _findBestVoice(voices, ['en-US', 'en-GB', 'en']),
        'fr': _findBestVoice(voices, ['fr-FR', 'fr-CA', 'fr']),
        'de': _findBestVoice(voices, ['de-DE', 'de-AT', 'de']),
        'es': _findBestVoice(voices, ['es-ES', 'es-MX', 'es']),
        'it': _findBestVoice(voices, ['it-IT', 'it']),
        'ja': _findBestVoice(voices, ['ja-JP', 'ja']),
        'zh': _findBestVoice(voices, ['zh-CN', 'zh-TW', 'zh']),
        'tr': _findBestVoice(voices, ['tr-TR', 'tr']),
        'ru': _findBestVoice(voices, ['ru-RU', 'ru']),
      };

      debugPrint('🎤 Available voices configured: ${languageVoices.keys}');
    }
  }

  /// العثور على أفضل صوت للغة
  Map<String, String>? _findBestVoice(
    List<dynamic> voices,
    List<String> preferences,
  ) {
    for (final preference in preferences) {
      for (final voice in voices) {
        if (voice['locale']?.toString().startsWith(preference) == true) {
          return {
            'name': voice['name']?.toString() ?? '',
            'locale': voice['locale']?.toString() ?? '',
          };
        }
      }
    }
    return null;
  }

  /// تشغيل النص بصوت طبيعي
  Future<void> speak(
    String text, {
    String? language,
    double? rate,
    double? pitch,
  }) async {
    if (!_isInitialized) await initialize();

    try {
      // تغيير اللغة إذا لزم الأمر
      if (language != null && language != _currentLanguage) {
        await _setLanguage(language);
      }

      // تعديل السرعة والنبرة
      if (rate != null) await _tts.setSpeechRate(rate);
      if (pitch != null) await _tts.setPitch(pitch);

      // تشغيل النص
      await _tts.speak(text);

      debugPrint('🔊 Speaking: "$text" in $_currentLanguage');
    } catch (e) {
      debugPrint('❌ Error speaking text: $e');
    }
  }

  /// تغيير اللغة
  Future<void> _setLanguage(String language) async {
    final languageSettings = _getLanguageSettings(language);

    await _tts.setLanguage(languageSettings['locale']!);
    await _tts.setSpeechRate(double.parse(languageSettings['rate']!));
    await _tts.setPitch(double.parse(languageSettings['pitch']!));

    _currentLanguage = language;
    _speechRate = double.parse(languageSettings['rate']!);
    _pitch = double.parse(languageSettings['pitch']!);
  }

  /// إعدادات اللغة المحسنة
  Map<String, String> _getLanguageSettings(String language) {
    switch (language) {
      case 'ar':
        return {'locale': 'ar-SA', 'rate': '0.4', 'pitch': '1.0'};
      case 'en':
        return {'locale': 'en-US', 'rate': '0.5', 'pitch': '1.0'};
      case 'fr':
        return {'locale': 'fr-FR', 'rate': '0.5', 'pitch': '1.1'};
      case 'de':
        return {'locale': 'de-DE', 'rate': '0.5', 'pitch': '0.9'};
      case 'es':
        return {'locale': 'es-ES', 'rate': '0.5', 'pitch': '1.1'};
      case 'it':
        return {'locale': 'it-IT', 'rate': '0.5', 'pitch': '1.2'};
      case 'ja':
        return {'locale': 'ja-JP', 'rate': '0.4', 'pitch': '1.3'};
      case 'zh':
        return {'locale': 'zh-CN', 'rate': '0.4', 'pitch': '1.2'};
      case 'tr':
        return {'locale': 'tr-TR', 'rate': '0.5', 'pitch': '1.0'};
      case 'ru':
        return {'locale': 'ru-RU', 'rate': '0.5', 'pitch': '0.9'};
      default:
        return {'locale': 'en-US', 'rate': '0.5', 'pitch': '1.0'};
    }
  }

  /// تشغيل أصوات التأثيرات (مثل Duolingo)
  Future<void> playEffect(AudioEffect effect) async {
    try {
      // محاولة تشغيل الملف المولد أولاً
      final generatedFilePath = await _getGeneratedEffectPath(effect);
      if (generatedFilePath != null) {
        await _effectsPlayer.play(DeviceFileSource(generatedFilePath));
        return;
      }

      // محاولة تشغيل ملف الأصول
      final effectPath = _getEffectPath(effect);
      if (effectPath != null) {
        await _effectsPlayer.play(AssetSource(effectPath));
      } else {
        // استخدام مولد الأصوات الاحترافي
        await _playGeneratedEffect(effect);
      }
    } catch (e) {
      debugPrint('❌ Error playing effect: $e');
      await _playGeneratedEffect(effect);
    }
  }

  /// الحصول على مسار الملف المولد
  Future<String?> _getGeneratedEffectPath(AudioEffect effect) async {
    final fileName = _getGeneratedEffectFileName(effect);
    if (fileName == null) return null;

    final filePath = _fileManager.getAudioFilePath('effects', fileName);
    if (filePath == null) return null;

    final fileExists = await _fileManager.audioFileExists('effects', fileName);
    return fileExists ? filePath : null;
  }

  /// الحصول على اسم الملف المولد
  String? _getGeneratedEffectFileName(AudioEffect effect) {
    switch (effect) {
      case AudioEffect.correct:
        return 'correct.wav';
      case AudioEffect.incorrect:
        return 'incorrect.wav';
      case AudioEffect.levelComplete:
        return 'level_complete.wav';
      case AudioEffect.achievement:
        return 'achievement.wav';
      case AudioEffect.streak:
        return 'streak.wav';
      case AudioEffect.heartLost:
        return 'heart_lost.wav';
      case AudioEffect.buttonClick:
        return 'button_click.wav';
      case AudioEffect.notification:
        return 'notification.wav';
    }
  }

  /// مسارات أصوات التأثيرات (احتياطية)
  String? _getEffectPath(AudioEffect effect) {
    switch (effect) {
      case AudioEffect.correct:
        return 'audio/effects/correct.mp3';
      case AudioEffect.incorrect:
        return 'audio/effects/incorrect.mp3';
      case AudioEffect.levelComplete:
        return 'audio/effects/level_complete.mp3';
      case AudioEffect.achievement:
        return 'audio/effects/achievement.mp3';
      case AudioEffect.streak:
        return 'audio/effects/streak.mp3';
      case AudioEffect.heartLost:
        return 'audio/effects/heart_lost.mp3';
      case AudioEffect.buttonClick:
        return 'audio/effects/button_click.mp3';
      case AudioEffect.notification:
        return 'audio/effects/notification.mp3';
    }
  }

  /// تشغيل أصوات مولدة احترافية
  Future<void> _playGeneratedEffect(AudioEffect effect) async {
    switch (effect) {
      case AudioEffect.correct:
        await _audioGenerator.generateCorrectSound();
        break;
      case AudioEffect.incorrect:
        await _audioGenerator.generateIncorrectSound();
        break;
      case AudioEffect.levelComplete:
        await _audioGenerator.generateLevelCompleteSound();
        break;
      case AudioEffect.achievement:
        await _audioGenerator.generateAchievementSound();
        break;
      case AudioEffect.streak:
        await _audioGenerator.generateStreakSound();
        break;
      case AudioEffect.heartLost:
        await _audioGenerator.generateHeartLostSound();
        break;
      case AudioEffect.buttonClick:
        await _audioGenerator.generateButtonClickSound();
        break;
      case AudioEffect.notification:
        await _audioGenerator.generateNotificationSound();
        break;
    }
  }

  /// إيقاف جميع الأصوات
  Future<void> stopAll() async {
    await _tts.stop();
    await _audioPlayer.stop();
    await _effectsPlayer.stop();
  }

  /// تشغيل صوت الخلفية
  Future<void> playBackgroundMusic({bool loop = true}) async {
    try {
      await _audioPlayer.play(
        AssetSource('audio/background/duolingo_theme.mp3'),
        mode: loop ? PlayerMode.mediaPlayer : PlayerMode.lowLatency,
      );
      if (loop) {
        await _audioPlayer.setReleaseMode(ReleaseMode.loop);
      }
      await _audioPlayer.setVolume(0.3); // صوت خفيف للخلفية
    } catch (e) {
      debugPrint('❌ Error playing background music: $e');
      // استخدام الموسيقى المولدة كبديل
      await _backgroundMusic.playAmbientMusic();
    }
  }

  /// إيقاف صوت الخلفية
  Future<void> stopBackgroundMusic() async {
    await _audioPlayer.stop();
    await _backgroundMusic.stopMusic();
  }

  /// تشغيل صوت الكلمة مع تأثيرات
  Future<void> speakWordWithEffects(String word, String language) async {
    // تأثير صوتي قبل النطق
    await playEffect(AudioEffect.buttonClick);
    await Future.delayed(const Duration(milliseconds: 200));

    // محاولة تشغيل النطق المسجل أولاً
    try {
      await _languageAudio.playWordPronunciation(word, language);
    } catch (e) {
      // استخدام TTS كبديل
      await speak(word, language: language, rate: 0.4, pitch: 1.1);
    }
  }

  /// تشغيل تسلسل صوتي للإجابة الصحيحة
  Future<void> playCorrectAnswerSequence() async {
    await playEffect(AudioEffect.correct);
    await Future.delayed(const Duration(milliseconds: 300));
    await speak('ممتاز!', language: 'ar', rate: 0.6);
  }

  /// تشغيل تسلسل صوتي للإجابة الخاطئة
  Future<void> playIncorrectAnswerSequence() async {
    await playEffect(AudioEffect.incorrect);
    await Future.delayed(const Duration(milliseconds: 300));
    await speak('حاول مرة أخرى', language: 'ar', rate: 0.5);
  }

  /// تنظيف الموارد
  void dispose() {
    _tts.stop();
    _audioPlayer.dispose();
    _effectsPlayer.dispose();
  }
}

/// أنواع التأثيرات الصوتية
enum AudioEffect {
  correct,
  incorrect,
  levelComplete,
  achievement,
  streak,
  heartLost,
  buttonClick,
  notification,
}

/// ويدجت زر الصوت الاحترافي
class ProfessionalSoundButton extends StatefulWidget {
  final String text;
  final String language;
  final VoidCallback? onPressed;
  final double size;
  final Color? color;

  const ProfessionalSoundButton({
    super.key,
    required this.text,
    required this.language,
    this.onPressed,
    this.size = 48,
    this.color,
  });

  @override
  State<ProfessionalSoundButton> createState() =>
      _ProfessionalSoundButtonState();
}

class _ProfessionalSoundButtonState extends State<ProfessionalSoundButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _playSound() async {
    if (_isPlaying) return;

    setState(() => _isPlaying = true);
    _animationController.forward().then((_) {
      _animationController.reverse();
    });

    try {
      await ProfessionalAudioService().speakWordWithEffects(
        widget.text,
        widget.language,
      );
      widget.onPressed?.call();
    } finally {
      if (mounted) {
        setState(() => _isPlaying = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  widget.color ?? const Color(0xFF1CB0F6),
                  (widget.color ?? const Color(0xFF1CB0F6)).withValues(
                    alpha: 0.8,
                  ),
                ],
              ),
              borderRadius: BorderRadius.circular(widget.size / 2),
              boxShadow: [
                BoxShadow(
                  color: (widget.color ?? const Color(0xFF1CB0F6)).withValues(
                    alpha: 0.3,
                  ),
                  spreadRadius: 2,
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(widget.size / 2),
                onTap: _isPlaying ? null : _playSound,
                child: Center(
                  child:
                      _isPlaying
                          ? SizedBox(
                            width: widget.size * 0.4,
                            height: widget.size * 0.4,
                            child: const CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                          : Icon(
                            Icons.volume_up,
                            color: Colors.white,
                            size: widget.size * 0.5,
                          ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
