import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../utils/audio_file_generator.dart';

/// مدير الملفات الصوتية الاحترافي
class AudioFileManager {
  static final AudioFileManager _instance = AudioFileManager._internal();
  factory AudioFileManager() => _instance;
  AudioFileManager._internal();

  String? _audioDirectory;
  bool _isInitialized = false;

  /// تهيئة مدير الملفات الصوتية
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final appDir = await getApplicationDocumentsDirectory();
      _audioDirectory = '${appDir.path}/audio';
      
      // إنشاء مجلدات الصوت
      await _createAudioDirectories();
      
      // توليد الملفات الصوتية إذا لم تكن موجودة
      await _generateAudioFilesIfNeeded();
      
      _isInitialized = true;
      debugPrint('✅ Audio File Manager initialized');
    } catch (e) {
      debugPrint('❌ Error initializing Audio File Manager: $e');
    }
  }

  /// إنشاء مجلدات الصوت
  Future<void> _createAudioDirectories() async {
    if (_audioDirectory == null) return;

    final directories = [
      '$_audioDirectory/effects',
      '$_audioDirectory/background',
      '$_audioDirectory/speech/ar',
      '$_audioDirectory/speech/en',
      '$_audioDirectory/speech/fr',
      '$_audioDirectory/speech/de',
      '$_audioDirectory/speech/es',
      '$_audioDirectory/speech/it',
      '$_audioDirectory/speech/ja',
      '$_audioDirectory/speech/zh',
      '$_audioDirectory/speech/tr',
      '$_audioDirectory/speech/ru',
      '$_audioDirectory/ambient',
    ];

    for (final dirPath in directories) {
      final dir = Directory(dirPath);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
        debugPrint('📁 Created directory: $dirPath');
      }
    }
  }

  /// توليد الملفات الصوتية إذا لم تكن موجودة
  Future<void> _generateAudioFilesIfNeeded() async {
    if (_audioDirectory == null) return;

    try {
      // توليد أصوات التأثيرات
      await _generateEffectSounds();
      
      // توليد أصوات النطق
      await _generateSpeechSounds();
      
      // توليد الموسيقى الخلفية
      await _generateBackgroundMusic();
      
      // توليد الأصوات البيئية
      await _generateAmbientSounds();
      
      debugPrint('🎵 All audio files generated successfully');
    } catch (e) {
      debugPrint('❌ Error generating audio files: $e');
    }
  }

  /// توليد أصوات التأثيرات
  Future<void> _generateEffectSounds() async {
    final effectsDir = '$_audioDirectory/effects';
    final soundFiles = AudioFileGenerator.generateAllSoundFiles();

    for (final entry in soundFiles.entries) {
      final fileName = entry.key;
      final audioData = entry.value;
      final filePath = '$effectsDir/$fileName';
      
      final file = File(filePath);
      if (!await file.exists()) {
        await file.writeAsBytes(audioData);
        debugPrint('🔊 Generated effect sound: $fileName');
      }
    }
  }

  /// توليد أصوات النطق
  Future<void> _generateSpeechSounds() async {
    final pronunciations = LanguagePronunciationGenerator.generateBasicPronunciations();

    for (final wordEntry in pronunciations.entries) {
      final word = wordEntry.key;
      final languages = wordEntry.value;

      for (final langEntry in languages.entries) {
        final lang = langEntry.key;
        final audioData = langEntry.value;
        
        final filePath = '$_audioDirectory/speech/$lang/${word}.wav';
        final file = File(filePath);
        
        if (!await file.exists()) {
          await file.writeAsBytes(audioData);
          debugPrint('🗣️ Generated speech: $word in $lang');
        }
      }
    }
  }

  /// توليد الموسيقى الخلفية
  Future<void> _generateBackgroundMusic() async {
    final backgroundDir = '$_audioDirectory/background';
    
    // موسيقى خلفية هادئة
    final musicFile = File('$backgroundDir/duolingo_theme.wav');
    if (!await musicFile.exists()) {
      final musicData = AudioFileGenerator.generateBackgroundMusicFile();
      await musicFile.writeAsBytes(musicData);
      debugPrint('🎵 Generated background music');
    }
    
    // موسيقى التركيز
    final focusFile = File('$backgroundDir/focus_music.wav');
    if (!await focusFile.exists()) {
      final focusData = AmbientSoundGenerator.generateFocusMusic();
      await focusFile.writeAsBytes(focusData);
      debugPrint('🧘 Generated focus music');
    }
  }

  /// توليد الأصوات البيئية
  Future<void> _generateAmbientSounds() async {
    final ambientDir = '$_audioDirectory/ambient';
    
    // أصوات الطبيعة
    final natureFile = File('$ambientDir/nature.wav');
    if (!await natureFile.exists()) {
      final natureData = AmbientSoundGenerator.generateNatureSounds();
      await natureFile.writeAsBytes(natureData);
      debugPrint('🌿 Generated nature sounds');
    }
  }

  /// الحصول على مسار ملف صوتي
  String? getAudioFilePath(String category, String fileName) {
    if (_audioDirectory == null) return null;
    return '$_audioDirectory/$category/$fileName';
  }

  /// الحصول على مسار ملف نطق
  String? getSpeechFilePath(String language, String word) {
    if (_audioDirectory == null) return null;
    return '$_audioDirectory/speech/$language/$word.wav';
  }

  /// التحقق من وجود ملف صوتي
  Future<bool> audioFileExists(String category, String fileName) async {
    final filePath = getAudioFilePath(category, fileName);
    if (filePath == null) return false;
    
    final file = File(filePath);
    return await file.exists();
  }

  /// حذف جميع الملفات الصوتية (لإعادة التوليد)
  Future<void> clearAllAudioFiles() async {
    if (_audioDirectory == null) return;

    try {
      final audioDir = Directory(_audioDirectory!);
      if (await audioDir.exists()) {
        await audioDir.delete(recursive: true);
        debugPrint('🗑️ Cleared all audio files');
        
        // إعادة إنشاء المجلدات والملفات
        await _createAudioDirectories();
        await _generateAudioFilesIfNeeded();
      }
    } catch (e) {
      debugPrint('❌ Error clearing audio files: $e');
    }
  }

  /// الحصول على حجم مجلد الصوت
  Future<int> getAudioDirectorySize() async {
    if (_audioDirectory == null) return 0;

    try {
      final audioDir = Directory(_audioDirectory!);
      if (!await audioDir.exists()) return 0;

      int totalSize = 0;
      await for (final entity in audioDir.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }
      
      return totalSize;
    } catch (e) {
      debugPrint('❌ Error calculating directory size: $e');
      return 0;
    }
  }

  /// تصدير الملفات الصوتية (للمطورين)
  Future<Map<String, String>> exportAudioFilesList() async {
    if (_audioDirectory == null) return {};

    final filesList = <String, String>{};
    
    try {
      final audioDir = Directory(_audioDirectory!);
      if (!await audioDir.exists()) return {};

      await for (final entity in audioDir.list(recursive: true)) {
        if (entity is File && entity.path.endsWith('.wav')) {
          final relativePath = entity.path.replaceFirst('$_audioDirectory/', '');
          filesList[relativePath] = entity.path;
        }
      }
    } catch (e) {
      debugPrint('❌ Error exporting files list: $e');
    }

    return filesList;
  }

  /// إحصائيات الملفات الصوتية
  Future<AudioFilesStats> getAudioFilesStats() async {
    if (_audioDirectory == null) {
      return AudioFilesStats(
        totalFiles: 0,
        totalSize: 0,
        effectFiles: 0,
        speechFiles: 0,
        backgroundFiles: 0,
        ambientFiles: 0,
      );
    }

    try {
      final audioDir = Directory(_audioDirectory!);
      if (!await audioDir.exists()) {
        return AudioFilesStats(
          totalFiles: 0,
          totalSize: 0,
          effectFiles: 0,
          speechFiles: 0,
          backgroundFiles: 0,
          ambientFiles: 0,
        );
      }

      int totalFiles = 0;
      int totalSize = 0;
      int effectFiles = 0;
      int speechFiles = 0;
      int backgroundFiles = 0;
      int ambientFiles = 0;

      await for (final entity in audioDir.list(recursive: true)) {
        if (entity is File && entity.path.endsWith('.wav')) {
          totalFiles++;
          final stat = await entity.stat();
          totalSize += stat.size;

          if (entity.path.contains('/effects/')) {
            effectFiles++;
          } else if (entity.path.contains('/speech/')) {
            speechFiles++;
          } else if (entity.path.contains('/background/')) {
            backgroundFiles++;
          } else if (entity.path.contains('/ambient/')) {
            ambientFiles++;
          }
        }
      }

      return AudioFilesStats(
        totalFiles: totalFiles,
        totalSize: totalSize,
        effectFiles: effectFiles,
        speechFiles: speechFiles,
        backgroundFiles: backgroundFiles,
        ambientFiles: ambientFiles,
      );
    } catch (e) {
      debugPrint('❌ Error getting audio stats: $e');
      return AudioFilesStats(
        totalFiles: 0,
        totalSize: 0,
        effectFiles: 0,
        speechFiles: 0,
        backgroundFiles: 0,
        ambientFiles: 0,
      );
    }
  }
}

/// إحصائيات الملفات الصوتية
class AudioFilesStats {
  final int totalFiles;
  final int totalSize;
  final int effectFiles;
  final int speechFiles;
  final int backgroundFiles;
  final int ambientFiles;

  AudioFilesStats({
    required this.totalFiles,
    required this.totalSize,
    required this.effectFiles,
    required this.speechFiles,
    required this.backgroundFiles,
    required this.ambientFiles,
  });

  /// تحويل الحجم إلى نص قابل للقراءة
  String get formattedSize {
    if (totalSize < 1024) {
      return '$totalSize B';
    } else if (totalSize < 1024 * 1024) {
      return '${(totalSize / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  @override
  String toString() {
    return '''
Audio Files Statistics:
- Total Files: $totalFiles
- Total Size: $formattedSize
- Effect Files: $effectFiles
- Speech Files: $speechFiles
- Background Files: $backgroundFiles
- Ambient Files: $ambientFiles
''';
  }
}
